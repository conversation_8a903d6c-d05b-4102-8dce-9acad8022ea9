// Application State
let selectedServices = [];
let customerData = {};

// Check if running in Electron
const isElectron = typeof window !== 'undefined' && window.process && window.process.type;

// Electron specific functionality
if (isElectron) {
    const { ipc<PERSON>enderer } = require('electron');

    // Enhanced save/load functionality for Electron
    window.electronAPI = {
        saveFile: async (data, filename) => {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath: filename,
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: 'Vš<PERSON>ky súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const writeResult = await ipcRenderer.invoke('write-file', result.filePath, data);
                return writeResult;
            }
            return { success: false, canceled: true };
        },

        openFile: async () => {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: [
                    { name: 'JSON súbory', extensions: ['json'] },
                    { name: '<PERSON>š<PERSON><PERSON> súbory', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePaths.length > 0) {
                const readResult = await ipcRenderer.invoke('read-file', result.filePaths[0]);
                return readResult;
            }
            return { success: false, canceled: true };
        }
    };
}

// DOM Elements
const serviceCheckboxes = document.querySelectorAll('input[type="checkbox"][data-service]');
const customerForm = document.querySelector('.customer-section');
const selectedServicesList = document.getElementById('selectedServicesList');
const subtotalElement = document.getElementById('subtotal');
const vatElement = document.getElementById('vat');
const totalElement = document.getElementById('total');
const generatePDFButton = document.querySelector('.btn-generate');

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCalculation();
    initializeNavigation();

    // Check if jsPDF is loaded
    setTimeout(checkJsPDFAvailability, 1000);
});

function checkJsPDFAvailability() {
    if (typeof window.jspdf === 'undefined') {
        console.warn('jsPDF is not available, PDF generation will not work');
        const generateButton = document.querySelector('.btn-generate');
        if (generateButton) {
            generateButton.title = 'jsPDF knižnica nie je dostupná';
        }
    } else {
        console.log('jsPDF is available and ready');
    }
}

// Event Listeners
function initializeEventListeners() {
    // Service selection
    serviceCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleServiceChange);
    });

    // Custom input fields
    document.addEventListener('input', handleCustomInputChange);

    // Customer form
    const customerInputs = customerForm.querySelectorAll('input');
    customerInputs.forEach(input => {
        input.addEventListener('input', updateCustomerData);
    });

    // Generate PDF button
    generatePDFButton.addEventListener('click', generatePDF);

    // Navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
}

// Navigation
function initializeNavigation() {
    // Categories are handled by CSS classes now
}

function handleNavigation(e) {
    e.preventDefault();
    const targetId = e.currentTarget.getAttribute('href').substring(1);

    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    e.currentTarget.classList.add('active');

    // Show target category
    document.querySelectorAll('.service-category').forEach(category => {
        category.classList.remove('active');
    });

    const targetCategory = document.getElementById(targetId);
    if (targetCategory) {
        targetCategory.classList.add('active', 'fade-in');
    }
}

// Service Selection
function handleServiceChange(e) {
    const checkbox = e.target;
    const serviceName = checkbox.getAttribute('data-service');
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    const isCustom = checkbox.hasAttribute('data-custom');
    
    if (checkbox.checked) {
        let finalPrice = basePrice;
        
        // Handle custom pricing
        if (isCustom) {
            finalPrice = calculateCustomPrice(checkbox);
        }
        
        const service = {
            name: serviceName,
            price: finalPrice,
            basePrice: basePrice,
            isCustom: isCustom,
            element: checkbox
        };
        
        selectedServices.push(service);
        
        // Show custom input if needed
        showCustomInput(checkbox);
    } else {
        // Remove service from selection
        selectedServices = selectedServices.filter(service => service.name !== serviceName);
        
        // Hide custom input
        hideCustomInput(checkbox);
    }
    
    updateCalculation();
    updateSelectedServicesList();
}

function calculateCustomPrice(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input input');
    const multiplier = parseFloat(customInput.getAttribute('data-multiplier'));
    const additional = parseFloat(customInput.getAttribute('data-additional'));
    const basePrice = parseFloat(checkbox.getAttribute('data-price')) || 0;
    
    if (customInput && customInput.value) {
        const inputValue = parseFloat(customInput.value) || 0;
        
        if (multiplier) {
            return inputValue * multiplier;
        } else if (additional) {
            return inputValue + additional;
        }
    }
    
    return basePrice;
}

function handleCustomInputChange(e) {
    const input = e.target;
    if (input.closest('.custom-input')) {
        const serviceItem = input.closest('.service-item');
        const checkbox = serviceItem.querySelector('input[type="checkbox"]');
        
        if (checkbox && checkbox.checked) {
            // Update the service price
            const serviceName = checkbox.getAttribute('data-service');
            const newPrice = calculateCustomPrice(checkbox);
            
            // Update in selectedServices array
            const serviceIndex = selectedServices.findIndex(service => service.name === serviceName);
            if (serviceIndex !== -1) {
                selectedServices[serviceIndex].price = newPrice;
                updateCalculation();
                updateSelectedServicesList();
            }
        }
    }
}

function showCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'block';
        customInput.classList.add('slide-in');
    }
}

function hideCustomInput(checkbox) {
    const serviceItem = checkbox.closest('.service-item');
    const customInput = serviceItem.querySelector('.custom-input');
    if (customInput) {
        customInput.style.display = 'none';
        customInput.classList.remove('slide-in');
        // Reset input value
        const input = customInput.querySelector('input');
        if (input) {
            input.value = '';
        }
    }
}

// Calculation
function updateCalculation() {
    const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
    const vat = subtotal * 0.20; // 20% DPH
    const total = subtotal + vat;
    
    // Update display
    subtotalElement.textContent = formatPrice(subtotal);
    vatElement.textContent = formatPrice(vat);
    totalElement.textContent = formatPrice(total);
    
    // Enable/disable PDF button
    const hasServices = selectedServices.length > 0;
    const hasCustomerData = validateCustomerData();
    generatePDFButton.disabled = !hasServices || !hasCustomerData;
}

function updateSelectedServicesList() {
    if (selectedServices.length === 0) {
        selectedServicesList.innerHTML = '<p class="no-services">Žiadne služby nie sú vybrané</p>';
        return;
    }
    
    const servicesHTML = selectedServices.map(service => `
        <div class="selected-service">
            <span class="service-name-calc">${service.name}</span>
            <span class="service-price-calc">${formatPrice(service.price)}</span>
        </div>
    `).join('');
    
    selectedServicesList.innerHTML = servicesHTML;
}

function formatPrice(price) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(price);
}

// Customer Data
function updateCustomerData() {
    customerData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        cemetery: document.getElementById('cemetery').value
    };
    
    updateCalculation();
}

function validateCustomerData() {
    return customerData.name && 
           customerData.phone && 
           customerData.email && 
           isValidEmail(customerData.email);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// PDF Generation
function generatePDF() {
    if (selectedServices.length === 0) {
        alert('Prosím vyberte aspoň jednu službu.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Prosím vyplňte všetky povinné údaje o zákazníkovi.');
        return;
    }

    // Show loading state
    generatePDFButton.classList.add('loading');
    generatePDFButton.disabled = true;
    generatePDFButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generujem PDF...';

    try {
        // Check if jsPDF is available
        if (typeof window.jspdf === 'undefined') {
            throw new Error('jsPDF knižnica nie je dostupná');
        }

        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Set font with better Unicode support
        doc.setFont('helvetica');

        // Function to handle Slovak characters
        function fixSlovakText(text) {
            if (!text) return '';
            return text
                .replace(/č/g, 'c')
                .replace(/Č/g, 'C')
                .replace(/ť/g, 't')
                .replace(/Ť/g, 'T')
                .replace(/ň/g, 'n')
                .replace(/Ň/g, 'N')
                .replace(/ž/g, 'z')
                .replace(/Ž/g, 'Z')
                .replace(/š/g, 's')
                .replace(/Š/g, 'S')
                .replace(/ý/g, 'y')
                .replace(/Ý/g, 'Y')
                .replace(/á/g, 'a')
                .replace(/Á/g, 'A')
                .replace(/é/g, 'e')
                .replace(/É/g, 'E')
                .replace(/í/g, 'i')
                .replace(/Í/g, 'I')
                .replace(/ó/g, 'o')
                .replace(/Ó/g, 'O')
                .replace(/ú/g, 'u')
                .replace(/Ú/g, 'U')
                .replace(/ô/g, 'o')
                .replace(/Ô/g, 'O')
                .replace(/ľ/g, 'l')
                .replace(/Ľ/g, 'L')
                .replace(/ŕ/g, 'r')
                .replace(/Ŕ/g, 'R')
                .replace(/ä/g, 'a')
                .replace(/Ä/g, 'A')
                .replace(/ď/g, 'd')
                .replace(/Ď/g, 'D');
        }
        
        // Header
        doc.setFontSize(20);
        doc.setTextColor(45, 90, 61);
        doc.text(fixSlovakText('CENOVÁ PONUKA'), 105, 30, { align: 'center' });

        doc.setFontSize(14);
        doc.text(fixSlovakText('Starostlivosť o hrobové miesta'), 105, 40, { align: 'center' });
        
        // Customer information
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(fixSlovakText('ÚDAJE O ZÁKAZNÍKOVI:'), 20, 60);

        let yPos = 70;
        doc.setFontSize(10);
        doc.text(fixSlovakText(`Meno: ${customerData.name}`), 20, yPos);
        yPos += 7;
        doc.text(fixSlovakText(`Telefón: ${customerData.phone}`), 20, yPos);
        yPos += 7;
        doc.text(fixSlovakText(`Email: ${customerData.email}`), 20, yPos);
        yPos += 7;
        if (customerData.address) {
            doc.text(fixSlovakText(`Adresa: ${customerData.address}`), 20, yPos);
            yPos += 7;
        }
        if (customerData.cemetery) {
            doc.text(fixSlovakText(`Cintorín: ${customerData.cemetery}`), 20, yPos);
            yPos += 7;
        }
        
        // Services
        yPos += 10;
        doc.setFontSize(12);
        doc.text(fixSlovakText('VYBRANÉ SLUŽBY:'), 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        selectedServices.forEach((service, index) => {
            doc.text(fixSlovakText(`${index + 1}. ${service.name}`), 20, yPos);
            doc.text(formatPrice(service.price), 150, yPos);
            yPos += 7;
        });
        
        // Totals
        yPos += 10;
        const subtotal = selectedServices.reduce((sum, service) => sum + service.price, 0);
        const vat = subtotal * 0.20;
        const total = subtotal + vat;
        
        doc.line(20, yPos, 190, yPos);
        yPos += 10;

        doc.text(fixSlovakText('Súčet bez DPH:'), 20, yPos);
        doc.text(formatPrice(subtotal), 150, yPos);
        yPos += 7;

        doc.text('DPH 20%:', 20, yPos);
        doc.text(formatPrice(vat), 150, yPos);
        yPos += 7;

        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text(fixSlovakText('CELKOM S DPH:'), 20, yPos);
        doc.text(formatPrice(total), 150, yPos);

        // Footer
        yPos += 20;
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text(fixSlovakText('Platnosť ponuky: 30 dní'), 20, yPos);
        yPos += 7;
        doc.text(fixSlovakText(`Dátum vystavenia: ${new Date().toLocaleDateString('sk-SK')}`), 20, yPos);

        yPos += 15;
        doc.text('Kontakt: <EMAIL>', 20, yPos);
        yPos += 7;
        doc.text('Tel: +421 951 553 464', 20, yPos);
        
        // Save PDF
        const cleanName = fixSlovakText(customerData.name).replace(/\s+/g, '-').toLowerCase();
        const fileName = `cenova-ponuka-${cleanName}-${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
        
    } catch (error) {
        console.error('Error generating PDF:', error);
        let errorMessage = 'Nastala chyba pri generovaní PDF.';

        if (error.message.includes('jsPDF')) {
            errorMessage = 'jsPDF knižnica nie je dostupná. Skúste obnoviť aplikáciu.';
        } else if (error.message.includes('save')) {
            errorMessage = 'Chyba pri ukladaní PDF súboru.';
        }

        alert(errorMessage + '\n\nDetail chyby: ' + error.message);
    } finally {
        // Reset button state
        generatePDFButton.classList.remove('loading');
        generatePDFButton.disabled = false;
        generatePDFButton.innerHTML = '<i class="fas fa-file-pdf"></i> Generovať PDF ponuku';
        updateCalculation(); // This will re-enable the button if conditions are met
    }
}

// Save/Load Functions
async function saveQuote() {
    const quoteData = {
        customerData: customerData,
        selectedServices: selectedServices.map(service => ({
            name: service.name,
            price: service.price,
            basePrice: service.basePrice,
            isCustom: service.isCustom
        })),
        timestamp: new Date().toISOString()
    };

    const dataStr = JSON.stringify(quoteData, null, 2);
    const filename = `ponuka-${customerData.name || 'nova'}-${new Date().toISOString().split('T')[0]}.json`;

    if (isElectron && window.electronAPI) {
        // Use Electron's native save dialog
        try {
            const result = await window.electronAPI.saveFile(dataStr, filename);
            if (result.success) {
                alert('Ponuka bola úspešne uložená.');
            } else if (!result.canceled) {
                alert('Chyba pri ukladaní: ' + result.error);
            }
        } catch (error) {
            console.error('Error saving file:', error);
            alert('Chyba pri ukladaní súboru.');
        }
    } else {
        // Fallback to browser download
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();

        URL.revokeObjectURL(url);
    }
}

async function loadQuote() {
    if (isElectron && window.electronAPI) {
        // Use Electron's native open dialog
        try {
            const result = await window.electronAPI.openFile();
            if (result.success) {
                loadQuoteData(JSON.parse(result.data));
            } else if (!result.canceled) {
                alert('Chyba pri načítavaní: ' + result.error);
            }
        } catch (error) {
            console.error('Error loading file:', error);
            alert('Chyba pri načítavaní súboru. Prosím skontrolujte formát súboru.');
        }
    } else {
        // Fallback to browser file input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const quoteData = JSON.parse(e.target.result);
                        loadQuoteData(quoteData);
                    } catch (error) {
                        alert('Chyba pri načítavaní súboru. Prosím skontrolujte formát súboru.');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }
}

function loadQuoteData(quoteData) {
    // Load customer data
    customerData = quoteData.customerData;
    document.getElementById('customerName').value = customerData.name || '';
    document.getElementById('customerPhone').value = customerData.phone || '';
    document.getElementById('customerEmail').value = customerData.email || '';
    document.getElementById('customerAddress').value = customerData.address || '';
    document.getElementById('cemetery').value = customerData.cemetery || '';

    // Clear current selections
    serviceCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        hideCustomInput(checkbox);
    });
    selectedServices = [];

    // Load selected services
    quoteData.selectedServices.forEach(savedService => {
        const checkbox = document.querySelector(`input[data-service="${savedService.name}"]`);
        if (checkbox) {
            checkbox.checked = true;
            selectedServices.push({
                name: savedService.name,
                price: savedService.price,
                basePrice: savedService.basePrice,
                isCustom: savedService.isCustom,
                element: checkbox
            });

            if (savedService.isCustom) {
                showCustomInput(checkbox);
            }
        }
    });

    updateCalculation();
    updateSelectedServicesList();

    alert('Ponuka bola úspešne načítaná.');
}
