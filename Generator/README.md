# Gener<PERSON><PERSON> cenov<PERSON>ch ponúk - Starostlivosť o hrobové miesta

Moderná webová aplikácia pre generovanie profesionálnych cenových ponúk pre služby starostlivosti o hrobové miesta.

## 🚀 Funkcie

### ✨ Hlavné funkcionality
- **Responzívny dizajn** - funguje na desktop, tablet aj mobile zariadeniach
- **Live kalkulácia** - ceny sa prepočítavaju v reálnom čase
- **PDF generovanie** - profesionálne formátované cenové ponuky
- **Ukladanie/načítavanie** - možnosť uložiť a načítať ponuky
- **Validácia údajov** - kontrola povinných polí a formátu emailu

### 📋 Kategórie služieb

#### 🔧 Základné služby
- Základná údrž<PERSON> (<PERSON><PERSON><PERSON><PERSON> miest<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>)
- Hĺbkové čistenie (všetky typy hrobov)

#### 📦 Balíky služieb
- <PERSON><PERSON><PERSON> Svia<PERSON>č<PERSON>ý (ro<PERSON><PERSON><PERSON> balíč<PERSON> pre všetky typy hrobov)
- Balík Celoročný Premium (pre jednohrob a dvojhrob)

#### 📱 Digitálne služby - ESPOMIENKA
- QR kód balíčky (Základný, Rozšírený, Rodinný, Na mieru)
- Montáž QR kódu (Prilepenie, Vyleptanie)

#### ➕ Doplnkové služby
- Obnova farby písma (s kalkuláciou na znak)
- Dosekanie nového písma (s kalkuláciou na znak)
- Sezónna výsadba kvetov (cena kvetov + práca)
- Dosypanie kameniva/kôry (cena materiálu + práca)
- Samostatná impregnácia
- Donáška a aranžovanie

#### ⭐ Špeciálne ponuky
- Akcia 2+1 ZADARMO pre Balík Sviatočný
- Akcia 2+1 ZADARMO pre Balík Celoročný Premium

## 🛠️ Technické špecifikácie

### Frontend
- **HTML5** s modernou sémantickou štruktúrou
- **CSS3** s Flexbox a Grid layoutom
- **Vanilla JavaScript** (ES6+)
- **Google Fonts** (Inter) pre modernú typografiu
- **Font Awesome** ikony

### Knižnice
- **jsPDF** - generovanie PDF dokumentov
- **HTTP Server** - lokálny development server

### Farebná schéma
- **Primárna:** #2d5a3d (tmavo zelená)
- **Sekundárna:** #d4af37 (zlatá)
- **Text:** #333333
- **Pozadie:** #f8f9fa

## 📁 Štruktúra projektu

```
Generator/
├── index.html          # Hlavný HTML súbor
├── styles.css          # CSS štýly
├── script.js           # JavaScript funkcionalita
└── README.md           # Dokumentácia
```

## 🚀 Spustenie aplikácie

### Lokálne spustenie
1. Otvorte terminál v adresári `Generator`
2. Spustite HTTP server:
   ```bash
   python3 -m http.server 8000
   ```
3. Otvorte prehliadač a prejdite na `http://localhost:8000`

### Alternatívne spustenie
- Môžete použiť ľubovoľný HTTP server (Apache, Nginx, Node.js, atď.)
- Alebo jednoducho otvorte `index.html` v prehliadači (niektoré funkcie môžu byť obmedzené)

## 📖 Používanie aplikácie

### 1. Zadanie údajov o zákazníkovi
- Vyplňte povinné polia: Meno, Telefón, Email
- Voliteľne: Adresa, Cintorín/Lokalita

### 2. Výber služieb
- Navigujte medzi kategóriami pomocou bočného menu
- Označte požadované služby pomocou checkboxov
- Pre služby s variabilnou cenou zadajte potrebné údaje

### 3. Kontrola kalkulácie
- V pravom paneli vidíte vybrané služby
- Automatický prepočet DPH (20%)
- Celková suma s DPH

### 4. Generovanie PDF
- Tlačidlo sa aktivuje po vyplnení povinných údajov a výbere služieb
- PDF sa automaticky stiahne s názvom obsahujúcim meno zákazníka a dátum

### 5. Ukladanie/Načítavanie ponúk
- **Uložiť:** Exportuje ponuku ako JSON súbor
- **Načítať:** Importuje predtým uloženú ponuku

## 🎨 Dizajn a UX

### Responzívnosť
- **Desktop (1200px+):** 3-stĺpcový layout
- **Tablet (768px-1199px):** 2-stĺpcový layout
- **Mobile (<768px):** 1-stĺpcový layout s kolapsovaným menu

### Animácie
- Smooth transitions na hover efekty
- Fade-in animácie pri prepínaní kategórií
- Loading animácie pri generovaní PDF

### Accessibility
- Sémantické HTML elementy
- Keyboard navigation support
- Kontrastné farby pre lepšiu čitateľnosť

## 🔧 Prispôsobenie

### Zmena kontaktných údajov
V súbore `script.js` nájdite funkciu `generatePDF()` a upravte:
```javascript
doc.text('Kontakt: <EMAIL>', 20, yPos);
doc.text('Tel: +421 xxx xxx xxx', 20, yPos);
```

### Pridanie loga
1. Pridajte logo súbor do adresára
2. V `generatePDF()` funkcii pridajte:
```javascript
doc.addImage('logo.png', 'PNG', 20, 10, 50, 20);
```

### Úprava cien
Ceny sú definované v HTML atribútoch `data-price`. Pre zmenu ceny upravte príslušný element.

### Pridanie novej služby
1. Pridajte HTML element s checkboxom
2. Nastavte `data-price` a `data-service` atribúty
3. CSS štýly sa aplikujú automaticky

## 🐛 Riešenie problémov

### PDF sa negeneruje
- Skontrolujte, či sú vyplnené všetky povinné polia
- Overte, či je vybraná aspoň jedna služba
- Skontrolujte konzolu prehliadača pre chybové hlásenia

### Služby sa nezobrazujú
- Overte, či sú správne načítané CSS a JS súbory
- Skontrolujte, či funguje navigácia medzi kategóriami

### Responzívnosť nefunguje
- Overte, či je nastavený správny viewport meta tag
- Skontrolujte CSS media queries

## 📝 Licencia

Tento projekt je vytvorený pre interné použitie. Všetky práva vyhradené.

## 🤝 Podpora

Pre technickú podporu alebo otázky kontaktujte vývojára.

---

**Verzia:** 1.0.0  
**Posledná aktualizácia:** December 2024  
**Kompatibilita:** Moderné prehliadače (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
