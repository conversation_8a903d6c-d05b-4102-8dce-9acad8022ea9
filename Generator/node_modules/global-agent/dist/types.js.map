{"version": 3, "sources": ["../src/types.js"], "names": [], "mappings": ";;AAEA;;AAGA;;AAGA;;AAGA", "sourcesContent": ["// @flow\n\nimport {\n  Socket,\n} from 'net';\nimport {\n  TLSSocket,\n} from 'tls';\nimport {\n  Agent as HttpAgent,\n} from 'http';\nimport {\n  Agent as HttpsAgent,\n} from 'https';\n\nexport type ProxyConfigurationType = {|\n  +authorization: string,\n  +hostname: string,\n  +port: number,\n|};\n\nexport type TlsConfigurationType = {|\n  +ca?: string,\n  +cert?: string,\n  +ciphers?: string,\n  +clientCertEngine?: string,\n  +crl?: string,\n  +dhparam?: string,\n  +ecdhCurve?: string,\n  +honorCipherOrder?: boolean,\n  +key?: string,\n  +passphrase?: string,\n  +pfx?: string,\n  +rejectUnauthorized?: boolean,\n  +secureOptions?: number,\n  +secureProtocol?: string,\n  +servername?: string,\n  +sessionIdContext?: string,\n|};\n\nexport type ConnectionConfigurationType = {|\n  +host: string,\n  +port: number,\n  +tls?: TlsConfigurationType,\n  +proxy: ProxyConfigurationType,\n|};\n\nexport type ConnectionCallbackType = (error: Error | null, socket?: Socket | TLSSocket) => void;\n\nexport type AgentType = HttpAgent | HttpsAgent;\nexport type IsProxyConfiguredMethodType = () => boolean;\nexport type MustUrlUseProxyMethodType = (url: string) => boolean;\nexport type GetUrlProxyMethodType = (url: string) => ProxyConfigurationType;\nexport type ProtocolType = 'http:' | 'https:';\n\nexport type ProxyAgentConfigurationInputType = {|\n  +environmentVariableNamespace?: string,\n  +forceGlobalAgent?: boolean,\n  +socketConnectionTimeout?: number,\n|};\n\nexport type ProxyAgentConfigurationType = {|\n  +environmentVariableNamespace: string,\n  +forceGlobalAgent: boolean,\n  +socketConnectionTimeout: number,\n|};\n"], "file": "types.js"}