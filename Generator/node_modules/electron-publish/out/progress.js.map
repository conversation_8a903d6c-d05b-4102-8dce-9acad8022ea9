{"version": 3, "file": "progress.js", "sourceRoot": "", "sources": ["../src/progress.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,MAAsB,WAAW;IAe/B;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,YACmB,MAAc,EAC/B,UAAe,EAAE;QADA,WAAM,GAAN,MAAM,CAAQ;QArCzB,YAAO,GAAG,CAAC,CAAA;QACnB,UAAK,GAAG,CAAC,CAAA;QAID,WAAM,GAAQ,IAAI,CAAA;QAClB,aAAQ,GAAG,EAAE,CAAA;QAEb,UAAK,GAAG,CAAC,CAAA;QAET,aAAQ,GAAG,KAAK,CAAA;QA8BtB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAA;QAE9C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG;YACX,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,GAAG;YACjC,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,GAAG;YACrC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,IAAI,GAAG;SAC9C,CAAA;IACH,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,KAAa;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;IAC3C,CAAC;IAED,IAAI,aAAa,CAAC,KAAa;QAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QAEpB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAA;QAEb,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YACpB,IAAI,CAAC,SAAS,EAAE,CAAA;QAClB,CAAC;IACH,CAAC;IAED,MAAM;QACJ,qBAAqB;QACrB,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACzB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAEjE,MAAM,OAAO,GAAG,KAAK,GAAG,GAAG,CAAA;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;QACvC,MAAM,GAAG,GAAG,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAA;QAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;QAE5C,+DAA+D;QAC/D,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM;aAClB,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;aAC5C,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;aACxC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACzE,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aAC/E,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aAC7C,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;QAEhD,qDAAqD;QACrD,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;QACtF,IAAI,cAAc,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACnD,cAAc,IAAI,CAAC,CAAA;QACrB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAA;QAChD,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC/E,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QAE7F,qCAAqC;QACrC,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,QAAQ,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QACzD,CAAC;QAED,qCAAqC;QACrC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,GAAG,UAAU,CAAC,CAAA;QAEhD,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YACxB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YAChD,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;YACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACtB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;QACrB,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,KAAa;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3C,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,OAAe;QACvB,yBAAyB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,CAAC,SAAS,EAAE,CAAA;QAClB,2CAA2C;QAC3C,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAClB,yBAAyB;QACzB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACrB,+CAA+C;QAC/C,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAClB,gDAAgD;QAChD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7B,CAAC;CAGF;AArKD,kCAqKC;AAED,MAAa,gBAAgB;IAI3B,YAA6B,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;QAH7C,UAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAClB,eAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;IAEkB,CAAC;IAEzD,MAAM,CAAC,WAAmB,EAAE,KAAa;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,KAAK,EAAE,CAAC;YACnD,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAA;YAE5B,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAA;YAC9B,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,WAAW,CAAA;QAC9C,CAAC;IACH,CAAC;CACF;AAfD,4CAeC", "sourcesContent": ["/*!\n * node-progress\n * Copyright(c) 2011 T<PERSON> <<EMAIL>>\n * MIT Licensed\n */\n\nexport abstract class ProgressBar {\n  private readonly stream: any\n\n  private current = 0\n  total = 0\n  private readonly width: number\n\n  private chars: any\n  private tokens: any = null\n  private lastDraw = \"\"\n\n  private start = 0\n\n  private complete = false\n\n  /**\n   * Initialize a `ProgressBar` with the given `fmt` string and `options` or`total`.\n   *\n   * Options:\n   *   - `curr` current completed index\n   *   - `total` total number of ticks to complete\n   *   - `width` the displayed width of the progress bar defaulting to total\n   *   - `stream` the output stream defaulting to stderr\n   *   - `head` head character defaulting to complete character\n   *   - `complete` completion character defaulting to \"=\"\n   *   - `incomplete` incomplete character defaulting to \"-\"\n   *   - `renderThrottle` minimum time between updates in milliseconds defaulting to 16\n   *   - `callback` optional function to call when the progress bar completes\n   *   - `clear` will clear the progress bar upon termination\n   *\n   * Tokens:\n   *   - `:bar` the progress bar itself\n   *   - `:current` current tick number\n   *   - `:total` total ticks\n   *   - `:elapsed` time elapsed in seconds\n   *   - `:percent` completion percentage\n   *   - `:eta` eta in seconds\n   *   - `:rate` rate of ticks per second\n   */\n  constructor(\n    private readonly format: string,\n    options: any = {}\n  ) {\n    this.stream = options.stream || process.stderr\n\n    this.total = options.total\n    this.width = options.width || this.total\n    this.chars = {\n      complete: options.complete || \"=\",\n      incomplete: options.incomplete || \"-\",\n      head: options.head || options.complete || \"=\",\n    }\n  }\n\n  /**\n   * \"tick\" the progress bar with optional `len` and optional `tokens`.\n   */\n  tick(delta: number) {\n    this.currentAmount = this.current + delta\n  }\n\n  set currentAmount(value: number) {\n    this.current = value\n\n    if (this.complete) {\n      return\n    }\n\n    this.render()\n\n    if (this.current >= this.total) {\n      this.complete = true\n      this.terminate()\n    }\n  }\n\n  render() {\n    // start time for eta\n    if (this.start === 0) {\n      this.start = Date.now()\n    }\n\n    const ratio = Math.min(Math.max(this.current / this.total, 0), 1)\n\n    const percent = ratio * 100\n    const elapsed = Date.now() - this.start\n    const eta = percent === 100 ? 0 : elapsed * (this.total / this.current - 1)\n    const rate = this.current / (elapsed / 1000)\n\n    /* populate the bar template with percentages and timestamps */\n    let str = this.format\n      .replace(\":current\", this.current.toString())\n      .replace(\":total\", this.total.toString())\n      .replace(\":elapsed\", isNaN(elapsed) ? \"0.0\" : (elapsed / 1000).toFixed(1))\n      .replace(\":eta\", isNaN(eta) || !isFinite(eta) ? \"0.0\" : (eta / 1000).toFixed(1))\n      .replace(\":percent\", percent.toFixed(0) + \"%\")\n      .replace(\":rate\", Math.round(rate).toString())\n\n    // compute the available space (non-zero) for the bar\n    let availableSpace = Math.max(0, this.stream.columns - str.replace(\":bar\", \"\").length)\n    if (availableSpace && process.platform === \"win32\") {\n      availableSpace -= 1\n    }\n\n    const width = Math.min(this.width, availableSpace)\n    const completeLength = Math.round(width * ratio)\n    let complete = Array(Math.max(0, completeLength + 1)).join(this.chars.complete)\n    const incomplete = Array(Math.max(0, width - completeLength + 1)).join(this.chars.incomplete)\n\n    /* add head to the complete string */\n    if (completeLength > 0) {\n      complete = `${complete.slice(0, -1)}${this.chars.head}`\n    }\n\n    /* fill in the actual progress bar */\n    str = str.replace(\":bar\", complete + incomplete)\n\n    /* replace the extra tokens */\n    if (this.tokens != null) {\n      for (const key of Object.keys(this.tokens)) {\n        str = str.replace(`:${key}`, this.tokens[key])\n      }\n    }\n\n    if (this.lastDraw !== str) {\n      this.stream.cursorTo(0)\n      this.stream.write(str)\n      this.stream.clearLine(1)\n      this.lastDraw = str\n    }\n  }\n\n  /**\n   * \"update\" the progress bar to represent an exact percentage.\n   * The ratio (between 0 and 1) specified will be multiplied by `total` and\n   * floored, representing the closest available \"tick.\" For example, if a\n   * progress bar has a length of 3 and `update(0.5)` is called, the progress\n   * will be set to 1.\n   *\n   * A ratio of 0.5 will attempt to set the progress to halfway.\n   */\n  update(ratio: number) {\n    const goal = Math.floor(ratio * this.total)\n    const delta = goal - this.current\n    this.tick(delta)\n  }\n\n  /**\n   * \"interrupt\" the progress bar and write a message above it.\n   */\n  interrupt(message: string) {\n    // clear the current line\n    const stream = this.stream\n    stream.clearLine()\n    // move the cursor to the start of the line\n    stream.cursorTo(0)\n    // write the message text\n    stream.write(message)\n    // terminate the line after writing the message\n    stream.write(\"\\n\")\n    // re-display the progress bar with its lastDraw\n    stream.write(this.lastDraw)\n  }\n\n  abstract terminate(): void\n}\n\nexport class ProgressCallback {\n  private start = Date.now()\n  private nextUpdate = this.start + 1000\n\n  constructor(private readonly progressBar: ProgressBar) {}\n\n  update(transferred: number, total: number) {\n    const now = Date.now()\n    if (now >= this.nextUpdate || transferred >= total) {\n      this.nextUpdate = now + 1000\n\n      this.progressBar.total = total\n      this.progressBar.currentAmount = transferred\n    }\n  }\n}\n"]}