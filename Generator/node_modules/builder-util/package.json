{"name": "builder-util", "version": "24.13.1", "main": "out/util.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/builder-util"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out"], "dependencies": {"7zip-bin": "~5.2.0", "@types/debug": "^4.1.6", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0", "builder-util-runtime": "9.2.4"}, "typings": "./out/util.d.ts", "devDependencies": {"@types/cross-spawn": "6.0.2", "@types/fs-extra": "^9.0.11", "@types/is-ci": "3.0.0", "@types/js-yaml": "4.0.3", "@types/source-map-support": "0.5.4"}}