# Contributing

This repository uses GitHub pull requests for code review.

See the [Joyent Engineering
Guidelines](https://github.com/joyent/eng/blob/master/docs/index.md) for general
best practices expected in this repository.

Contributions should be "make prepush" clean.  This target requires separate
tools:

* https://github.com/davepacheco/jsstyle
* https://github.com/davepacheco/javascriptlint
* https://github.com/joyent/catest

If you're changing something non-trivial or user-facing, you may want to submit
an issue first.
