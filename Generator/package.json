{"name": "cenove-ponuky-generator", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> ponúk pre starostlivosť o hrobové miesta", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["c<PERSON><PERSON><PERSON> pon<PERSON>y", "hrobové miesta", "PDF generátor", "desktop aplikácia"], "author": {"name": "<PERSON><PERSON><PERSON> n<PERSON>y", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {}, "build": {"appId": "com.firma.cenove-ponuky", "productName": "CenovePonuky", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist", "!.git", "!README.md"], "mac": {"category": "public.app-category.business", "icon": "assets/icon.icns", "identity": null, "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "assets/icon.ico", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"icon": "assets/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "CenovePonuky"}, "dmg": {"title": "CenovePonuky", "backgroundColor": "#2d5a3d"}}, "homepage": ".", "repository": {"type": "git", "url": "https://github.com/your-username/cenove-ponuky-generator.git"}}