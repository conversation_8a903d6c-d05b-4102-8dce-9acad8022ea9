<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF generovanie</title>
    <script src="node_modules/jspdf/dist/jspdf.umd.min.js"></script>
</head>
<body>
    <h1>Test PDF generovanie</h1>
    <button onclick="testPDF()">Generovať test PDF</button>
    
    <script>
        function testPDF() {
            try {
                console.log('Testing PDF generation...');
                
                if (typeof window.jspdf === 'undefined') {
                    alert('jsPDF nie je dostupné');
                    return;
                }
                
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFont('helvetica');
                doc.setFontSize(20);
                doc.text('Test PDF', 20, 30);
                doc.setFontSize(12);
                doc.text('Toto je test PDF sú<PERSON> vytvorený v Electron aplikácii.', 20, 50);
                
                doc.save('test.pdf');
                alert('PDF bolo úspešne vytvorené!');
                
            } catch (error) {
                console.error('Error:', error);
                alert('Chyba: ' + error.message);
            }
        }
    </script>
</body>
</html>
